'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
  ErrorDisplay,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/src/app/_components';
import { PhoneInput } from '@/src/app/_components/Common/PhoneInput';
import { usePhoneVerification } from '@/src/app/_hooks';
import { validatePhoneNumber } from '@/src/app/_utils/validation/phoneValidation';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

// Form validation schema using custom validator
const phoneVerificationSchema = z.object({
  phone: z
    .string()
    .min(1, 'Insira um número de telefone válido com DDD.')
    .refine(
      (value) => {
        const { isValid } = validatePhoneNumber(value, '+55');
        return isValid;
      },
      {
        message: 'Insira um número de telefone válido com DDD.',
      }
    ),
});

type PhoneVerificationFormValues = z.infer<typeof phoneVerificationSchema>;

interface PhoneVerificationFormProps {
  onVerificationSent: () => void;
}

export function PhoneVerificationForm({ onVerificationSent }: PhoneVerificationFormProps) {
  const { sendVerificationCode, isLoading, error } = usePhoneVerification();
  const [phoneInputError, setPhoneInputError] = useState<string | null>(null);
  const [formError, setFormError] = useState<Error | null>(null);

  // Initialize form
  const form = useForm<PhoneVerificationFormValues>({
    resolver: zodResolver(phoneVerificationSchema),
    defaultValues: {
      phone: '',
    },
  });

  // Handle form submission
  const onSubmit = async (values: PhoneVerificationFormValues) => {
    try {
      setFormError(null);

      // Validate phone number using our centralized utility
      const validationResult = validatePhoneNumber(values.phone, '+55');
      if (!validationResult.isValid) {
        setPhoneInputError(validationResult.error);
        return;
      }

      const success = await sendVerificationCode(values.phone);
      if (success) {
        onVerificationSent();
      }
    } catch (err) {
      console.error('Error submitting phone verification form:', err);
      setFormError(err instanceof Error ? err : new Error('Erro ao enviar código de verificação'));
    }
  };

  // If there's a form error, show the error display
  if (formError) {
    return (
      <ErrorDisplay
        error={formError}
        message="Erro ao enviar código de verificação. Por favor, tente novamente."
        onRetry={() => setFormError(null)}
      />
    );
  }

  return (
    <Card className="w-full max-w-md shadow-xl">
      {/* Wrap the form with Form component (FormProvider) */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardHeader>
            <div className="flex flex-col gap-2 md:flex-row md:items-center">
              <CardTitle className="text-4xl font-bold leading-tight">Entrar</CardTitle>
            </div>
            <CardDescription className="text-base">
              Acesse seus agendamentos já realizados com seu número de celular.{' '}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className={phoneInputError ? 'text-red-500' : ''}>Celular</FormLabel>
                  <FormControl>
                    <PhoneInput
                      value={field.value}
                      placeholder="(00) 00000-0000"
                      onChange={(value: string) => {
                        field.onChange(value);
                        form.trigger('phone');
                      }}
                      onValidationError={setPhoneInputError}
                      triggerValidation={() => form.trigger('phone')}
                    />
                  </FormControl>
                  {!phoneInputError && <FormMessage />}
                </FormItem>
              )}
            />
            {/* Use ErrorDisplay for API errors */}
            {error && <ErrorDisplay message={error} fullPage={false} />}
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            <Button
              type="submit"
              className="w-full rounded-xl"
              disabled={isLoading || !!phoneInputError}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Enviando...
                </>
              ) : (
                'Enviar código de verificação'
              )}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
