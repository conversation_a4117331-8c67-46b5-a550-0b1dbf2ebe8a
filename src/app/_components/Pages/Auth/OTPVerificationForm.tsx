'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from '@/src/app/_components';
import { OTPInput } from '@/src/app/_components/Pages/Auth/OTPInput';
import { usePhoneVerification } from '@/src/app/_hooks';
import { formatPhoneNumber } from '@/src/app/_utils';
import { ArrowLeft, Loader2, ShieldCheckIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface OTPVerificationFormProps {
  onBack: () => void;
}

export function OTPVerificationForm({ onBack }: OTPVerificationFormProps) {
  const router = useRouter();
  const { verifyCode, isLoading, error, phoneNumber } = usePhoneVerification();
  const [otpValue, setOtpValue] = useState('');

  // Format phone number for display
  const formattedPhone = formatPhoneNumber(phoneNumber, '+55');

  // Auto-validate OTP when all digits are filled
  useEffect(() => {
    if (otpValue.length === 6) {
      handleVerify();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [otpValue]);

  // Handle OTP verification
  const handleVerify = async () => {
    if (otpValue.length === 6) {
      const success = await verifyCode(otpValue);
      if (success) {
        // Redirect to schedules page after successful verification
        router.push('/agendamentos');
      }
    }
  };

  // Handle resend code
  const handleResendCode = () => {
    // In a real implementation, this would call an API to resend the code
    // For now, we'll just reset the OTP input
    setOtpValue('');
  };

  return (
    <div className="flex min-h-[420px] w-full max-w-[420px] flex-col items-center justify-center rounded-2xl bg-white px-8 py-10 shadow-md">
      {/* Icon and Title */}
      <div className="flex flex-col items-center gap-4 mb-6">
        {/* Use Figma's lock icon, fallback to ShieldCheckIcon if not available */}
        <ShieldCheckIcon className="h-8 w-8 text-[#FDC201]" strokeWidth={2.5} />
        <h2 className="text-[22px] font-bold text-[#0F172A] text-center">Confirme seu código</h2>
      </div>
      {/* Message */}
      <div className="mb-8 w-full text-center">
        <p className="text-[16px] font-normal text-[#334155] leading-snug">
          Insira o código de 6 dígitos enviado para <span className="font-semibold">{formattedPhone || 'seu celular'}</span> para confirmar seu acesso.
        </p>
      </div>
      {/* OTP Input */}
      <div className="mb-6 w-full flex justify-center">
        <OTPInput value={otpValue} onChange={setOtpValue} maxLength={6} disabled={isLoading} error={!!error} />
      </div>
      {/* Error Message */}
      {error && (
        <div className="mb-4 w-full text-center text-[14px] font-medium text-[#FF3B3B]">
          {error}
        </div>
      )}
      {/* Resend Link */}
      <div className="mb-8 w-full text-center">
        <button
          type="button"
          onClick={handleResendCode}
          className="text-[15px] font-semibold text-[#FDC201] hover:underline disabled:opacity-60"
          disabled={isLoading}
        >
          Reenviar código
        </button>
      </div>
      {/* Buttons */}
      <div className="flex w-full flex-col gap-3">
        <button
          type="button"
          className="flex h-12 w-full items-center justify-center rounded-xl bg-[#FDC201] text-[16px] font-bold text-[#0F172A] transition-opacity disabled:opacity-60"
          onClick={handleVerify}
          disabled={otpValue.length !== 6 || isLoading}
        >
          {isLoading ? (
            <Loader2 className="mr-2 h-5 w-5 animate-spin text-[#0F172A]" />
          ) : null}
          Confirmar
        </button>
        <button
          type="button"
          className="flex h-12 w-full items-center justify-center rounded-xl border border-[#FDC201] bg-white text-[16px] font-bold text-[#FDC201] transition-opacity disabled:opacity-60"
          onClick={onBack}
          disabled={isLoading}
        >
          <ArrowLeft className="mr-2 h-5 w-5" />
          Voltar
        </button>
      </div>
    </div>
  );
}
