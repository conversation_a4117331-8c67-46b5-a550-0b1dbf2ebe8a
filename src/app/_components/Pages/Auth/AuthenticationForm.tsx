'use client';

import { ErrorDisplay } from '@/src/app/_components';
import { OTPVerificationForm } from '@/src/app/_components/Pages/Auth/OTPVerificationForm';
import { PhoneVerificationForm } from '@/src/app/_components/Pages/Auth/PhoneVerificationForm';
import { useAuth } from '@/src/app/_context/AuthContext';
import { usePhoneVerification } from '@/src/app/_hooks';
import { cn } from '@/src/app/_utils/utils';
import { AnimatePresence, motion } from 'framer-motion';
import { useState } from 'react';

interface AuthenticationFormProps {
  className?: string;
}

/**
 * Authentication form component that handles both phone verification and OTP verification steps
 */
export function AuthenticationForm({ className }: AuthenticationFormProps) {
  const [step, setStep] = useState<'phone' | 'otp'>('phone');
  const { resetVerification } = usePhoneVerification();
  const { auth } = useAuth();
  const [error, setError] = useState<Error | null>(null);

  // Handle transition to OTP verification step
  const handleVerificationSent = () => {
    try {
      setError(null);
      setStep('otp');
    } catch (err) {
      console.error('Error transitioning to OTP step:', err);
      setError(err instanceof Error ? err : new Error('Erro ao processar verificação'));
    }
  };

  // Handle going back to phone verification step
  const handleBack = () => {
    try {
      setError(null);
      setStep('phone');
      resetVerification();
    } catch (err) {
      console.error('Error going back to phone step:', err);
      setError(
        err instanceof Error ? err : new Error('Erro ao retornar para verificação de telefone')
      );
    }
  };

  // If there's an error, show the error display
  if (error) {
    return (
      <ErrorDisplay
        error={error}
        message="Ocorreu um erro durante o processo de autenticação. Por favor, tente novamente."
        onRetry={() => setError(null)}
      />
    );
  }

  // If user is already authenticated, show a message
  if (auth.isAuthenticated) {
    return (
      <div className={cn('mx-auto w-full max-w-md text-center', className)}>
        <h2 className="mb-4 text-xl font-bold">Você já está autenticado</h2>
        <p className="text-gray-600">Você já está logado no sistema.</p>
      </div>
    );
  }

  return (
    <div className={cn('mx-auto w-full max-w-md', className)}>
      <AnimatePresence mode="wait">
        {step === 'phone' ? (
          <motion.div
            key="phone-verification"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <PhoneVerificationForm onVerificationSent={handleVerificationSent} />
          </motion.div>
        ) : (
          <motion.div
            key="otp-verification"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            <OTPVerificationForm onBack={handleBack} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
