'use client';

import { AskForService, Button, Icon, IconName } from '@/src/app/_components';
import { useServiceContext } from '@/src/app/_context/ServiceContext';
import { categories } from '@/src/app/_data/categories';
import { User } from 'lucide-react';
import Link from 'next/link';

interface ServiceNavigationMenuMobileProps {
  onLinkClick?: () => void;
}

export function ServiceNavigationMenuMobile({ onLinkClick }: ServiceNavigationMenuMobileProps) {
  const { services } = useServiceContext();

  // Sort categories alphabetically by name
  const sortedServices =
    services && services.length > 0
      ? [...services].sort((a, b) => a.name.localeCompare(b.name))
      : [];

  return (
    <nav className="w-full" aria-label="Menu de serviços">
      {/* Title section with padding adjusted to match Figma */}
      <div>
        <h2 className="px-8 py-8 text-3xl font-extrabold text-gray-900">O que você precisa?</h2>
      </div>

      {/* Login button */}
      <div className="border-t border-gray-200 px-8 py-6">
        <Link href="/entrar" onClick={onLinkClick}>
          <Button
            variant="outline"
            className="w-full justify-start gap-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-gray-900"
          >
            <User className="h-5 w-5" />
            <span>Entrar</span>
          </Button>
        </Link>
      </div>

      {/* Services menu */}
      <div className="divide-y divide-gray-200 px-8">
        {sortedServices.map((category) => {
          const matched = categories.find((c) => c.id === category.slug);
          const iconName = matched ? matched.icon : 'CircleHelp';

          // Sort subcategories alphabetically by name
          const sortedSubcategories = [...(category.subcategories || [])].sort((a, b) =>
            a.name.localeCompare(b.name)
          );

          return (
            <div key={category.id} className="py-8">
              <div className="grid grid-cols-[128px_1fr] gap-10">
                {/* Category column */}
                <div className="flex items-center gap-2">
                  <div className="flex h-6 w-6 items-center justify-center">
                    <Icon name={iconName as IconName} className="h-5 w-5 text-gray-500" />
                  </div>
                  <span className="font-semibold text-gray-900">{category.name}</span>
                </div>

                {/* Subcategories column */}
                <ul className="space-y-2">
                  {sortedSubcategories.map((subcategory) => {
                    // Create the URL for the first service in the subcategory
                    const href =
                      subcategory.services && subcategory.services.length > 0
                        ? `/servicos/${subcategory.slug}?=${subcategory.services[0]?.slug}`
                        : `/servicos/${subcategory.slug}`;

                    return (
                      <li key={`${category.id}-${subcategory.id}`}>
                        <Link
                          href={href}
                          className="flex h-12 w-full items-center break-words text-left text-base font-medium text-gray-500 transition-colors hover:text-gray-900"
                          title={`Ver serviços de ${subcategory.name}`}
                          onClick={() => {
                            window.dataLayer = window.dataLayer || [];
                            window.tagManagerDataLayer =
                              window.tagManagerDataLayer || window.dataLayer;

                            const eventData = {
                              event: `menu_click_${subcategory.name.replace(/\s+/g, '_').toLowerCase()}`,
                            };

                            // Push to dataLayer
                            window.dataLayer.push(eventData);

                            // Push to tagManagerDataLayer if it's different from dataLayer
                            if (window.tagManagerDataLayer !== window.dataLayer) {
                              window.tagManagerDataLayer.push(eventData);
                            }

                            if (onLinkClick) onLinkClick();
                          }}
                        >
                          <span className="inline-block">{subcategory.name}</span>
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              </div>
            </div>
          );
        })}
      </div>

      {/* Ask for service section */}
      <section className="px-8 py-10">
        <AskForService variant="custom" showIcon />
      </section>
    </nav>
  );
}
