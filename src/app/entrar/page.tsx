import { AskForService } from '@/src/app/_components/Common/AskForService/AskForService';
import { ServiceNavigationMenuDesktop } from '@/src/app/_components/Common/Navigation/ServiceNavigationMenuDesktop';
import { ServiceNavigationMenuMobile } from '@/src/app/_components/Common/Navigation/ServiceNavigationMenuMobile';
import { AuthenticationForm } from '@/src/app/_components/Pages/Auth/AuthenticationForm';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Entrar | GetNinjas',
  description: 'Acesse sua conta para visualizar seus agendamentos.',
};

export default function LoginPage() {
  return (
    <main className="flex min-h-screen w-full flex-col items-center bg-[#F8FAFC] px-2 pb-0 pt-8">
      {/* Authentication Card */}
      <div className="mb-8 flex w-full justify-center md:mb-12">
        <div className="w-full max-w-md">
          <AuthenticationForm />
        </div>
      </div>
      {/* Service Menu */}
      <div className="mb-6 flex w-full justify-center px-8 md:mb-8">
        <div className="w-full">
          <div className="hidden md:block">
            <ServiceNavigationMenuDesktop />
          </div>
          <div className="md:hidden">
            <ServiceNavigationMenuMobile />
          </div>
        </div>
      </div>
      {/* Ask For Service Card */}
      <div className="mb-8 flex w-full justify-center px-24 md:mb-16">
        <div className="w-full">
          <AskForService variant="custom" showIcon />
        </div>
      </div>
    </main>
  );
}
